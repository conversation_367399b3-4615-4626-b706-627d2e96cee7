import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:cart-variant',
      keepAlive: true,
      order: 1000,
      title: $t('warehouse.rawMaterial.title'),
      perms: ['rawMaterial'],
    },
    name: 'rawMaterial',
    path: '/rawMaterial',
    children: [
      {
        meta: {
          title: $t('warehouse.rawMaterial.purchaseInv'),
          icon: 'mdi:import',
          // 保存当前页面状态
          keepAlive: true,
          perms: ['rawMaterial.purchaseInv'],
          buttons: [
            {
              name: 'rawMaterial.purchaseInv.confirm',
              meta: {
                icon: 'ic:baseline-add',
                title: $t('warehouse.rawMaterial.confirm'),
                perms: ['rawMaterial.purchaseInv.confirm'],
              },
            },
          ],

          order: 100,
        },
        name: 'purchaseInventory',
        path: '/purchaseInventory/index',
        component: () =>
          import(
            '#/views/warehouseManagement/rawMaterial/purchaseInventory/index.vue'
          ),
      },
    ],
  },
];

export default routes;
