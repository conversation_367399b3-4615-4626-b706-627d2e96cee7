<script lang="ts" setup>
import type { ColDef } from 'ag-grid-community';

import { computed, ref, watch } from 'vue';

import { ElNotification } from 'element-plus';

import { useVbenForm } from '#/adapter/form';
import { getPurchaseOrderDetailAll } from '#/api/purchase/imported';
import ClientGridComponent from '#/components/ag-grid/ClientGridComponent.vue';
import { $t } from '#/locales';
import selectData from '#/views/common/selectData.vue';
// 定义 props
interface Props {
  record: any;
  isUpdate: boolean;
}

const props = defineProps<Props>();

const selectDataRef = ref();

const isReadonly = computed(() => !props.isUpdate);

/**
 * 明细相关变量
 */
const detailGridRef = ref();
const detailRowData = ref<any[]>([]);

/**
 * 明细表格列定义
 */
const detailColumnDefs: ColDef[] = [
  {
    headerName: $t('purchase.seq'),
    field: 'seq',
    width: 80,
  },
  {
    headerName: $t('purchase.mCode'),
    field: 'mCode',
    width: 120,
  },
  {
    headerName: $t('purchase.itemCode'),
    field: 'itemCode',
    width: 120,
  },
  {
    headerName: $t('purchase.itemName'),
    field: 'itemName',
    width: 150,
  },
  {
    headerName: $t('purchase.specModel'),
    field: 'specModel',
    width: 120,
  },
  {
    headerName: $t('purchase.unit'),
    field: 'unit',
    width: 80,
  },
  {
    headerName: $t('purchase.qty'),
    field: 'qty',
    width: 100,
  },
  {
    headerName: $t('purchase.packageCount'),
    field: 'packageCount',
    width: 100,
  },
  {
    headerName: $t('purchase.grossWeight'),
    field: 'grossWeight',
    width: 100,
  },
  {
    headerName: $t('purchase.netWeight'),
    field: 'netWeight',
    width: 100,
  },
  {
    headerName: $t('purchase.volume'),
    field: 'volume',
    width: 100,
  },
  {
    headerName: $t('purchase.unitPrice'),
    field: 'unitPrice',
    width: 100,
  },
  {
    headerName: $t('purchase.amount'),
    field: 'amount',
    width: 100,
  },
  {
    headerName: $t('purchase.currency'),
    field: 'currency',
    width: 80,
  },
];

/**
 * 明细表格默认列配置
 */
const detailDefaultColDef = {
  sortable: true,
  filter: true,
  resizable: true,
  flex: 1,
};

/**
 * 明细表格行选择配置
 */
const detailRowSelection = {
  mode: 'singleRow' as const,
  checkboxes: false,
  headerCheckbox: false,
  enableClickSelection: true,
};

// 获取明细数据
const getDetailData = async () => {
  if (!props.record.id) return;

  try {
    const res = await getPurchaseOrderDetailAll({
      orderNo: props.record.orderNo,
    });
    if (Array.isArray(res)) {
      detailRowData.value = res;
      setTimeout(() => {
        if (detailGridRef.value?.gridApi) {
          detailGridRef.value.gridApi.autoSizeAllColumns();
        }
      }, 10);
    }
  } catch {
    ElNotification({
      duration: 2500,
      message: '获取明细数据失败',
      type: 'error',
    });
  }
};

const selectOpen = async () => {
  try {
    selectDataRef.value.modalApi.open();
    // 监听弹窗关闭事件
    selectDataRef.value.modalApi.onClosed = () => {
      const selectedData = selectDataRef.value.modalApi.sharedData;
      if (selectedData && selectedData.length > 0) {
        const company = selectedData[0];

        // 映射API字段到表单的所有字段（根据实际API返回的字段名）
        const mappedData = {
          // 基本公司信息 - 从API获取
          supplier: company.companyCode || '',
        };
        // 更新表单数据
        formApi.setValues(mappedData);
      }
    };
  } catch (error) {
    console.error('Error in selectOpen:', error);
    ElNotification({
      type: 'error',
      message: '打开选择窗口失败',
      duration: 2500,
    });
  }
};

/**
 * 创建表单组件
 */
const [AddForm, formApi] = useVbenForm({
  showDefaultActions: false,
  commonConfig: {
    componentProps: {
      class: 'w-full',
      readonly: true,
    },
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4',
  schema: [
    {
      fieldName: 'orderNo',
      component: 'Input',
      label: $t('purchase.importOrderNo'),
      componentProps: {
        readonly: true,
      },
    },
    {
      fieldName: 'orderStatus',
      component: 'Input',
      label: $t('purchase.orderStatus'),
      componentProps: {
        readonly: true,
      },
    },
    {
      fieldName: 'poNo',
      component: 'Input',
      label: $t('purchase.poNo'),
    },
    {
      fieldName: 'declarationNo',
      component: 'Input',
      componentProps: {
        readonly: isReadonly,
      },
      label: $t('purchase.declarationNo'),
    },
    {
      fieldName: 'supplier',
      component: 'SearchInput',
      label: $t('purchase.supplier'),
      componentProps: {
        searchButtonProps: {
          disabled: isReadonly,
        },
        onSearch: async () => {
          await selectOpen();
        },
      },
    },
    {
      fieldName: 'receiptDate',
      component: 'DatePicker',
      label: $t('purchase.receiptDate'),
    },
    {
      fieldName: 'totalAmount',
      component: 'Input',
      label: $t('purchase.totalAmount'),
    },
    {
      fieldName: 'totalGrossWeight',
      component: 'Input',
      label: $t('purchase.totalGrossWeight'),
    },
    {
      fieldName: 'totalNetWeight',
      component: 'Input',
      label: $t('purchase.totalNetWeight'),
    },
    {
      fieldName: 'confirmUserName',
      component: 'Input',
      label: $t('purchase.confirmUserName'),
      componentProps: {
        readonly: true,
      },
    },
    {
      fieldName: 'confirmDateTime',
      component: 'Input',
      label: $t('purchase.confirmDateTime'),
      componentProps: {
        readonly: true,
      },
    },
    {
      fieldName: 'remark',
      component: 'Input',
      label: $t('purchase.remark'),
      componentProps: {
        readonly: isReadonly,
        type: 'textarea',
      },
    },
  ],
});

const selectConfig = {
  api: '/company/getCompanyList',
  columns: [
    { headerName: '公司代码', field: 'companyCode', width: 140 },
    { headerName: '公司税号', field: 'taxCode', width: 140 },
    { headerName: '公司名称', field: 'companyName', width: 140 },
    { headerName: '邮政编码', field: 'postalCode', width: 155 },
    { headerName: '公司名称', field: 'address', width: 135 },
  ],
  title: $t('basic.pleaseSelect'),
  showSearch: true,
  immediate: false,
  searchPlaceholder: $t('basic.selectInput'),
  multiple: false,
  class: 'w-[50%] h-[70%]',
};

// 监听 record 变化，获取明细数据
watch(
  () => props.record,
  (newRecord) => {
    if (newRecord?.id) {
      getDetailData();
    } else {
      detailRowData.value = [];
    }
  },
  { immediate: true },
);

// 暴露方法给父组件
defineExpose({
  formApi,
  getDetailData,
});
</script>

<template>
  <selectData ref="selectDataRef" v-bind="selectConfig" />
  <div class="flex h-full flex-col p-4">
    <!-- 主表单区域 -->
    <div class="mb-4 flex-shrink-0">
      <AddForm />
    </div>

    <!-- 明细区域 -->
    <div class="flex flex-1 flex-col">
      <div class="mb-2">
        <h3 class="text-lg font-medium">
          {{ $t('purchase.detailList') }}
        </h3>
      </div>

      <!-- 明细表格 -->
      <div class="flex-1">
        <ClientGridComponent
          ref="detailGridRef"
          :column-defs="detailColumnDefs"
          :row-data="detailRowData"
          :page-size="20"
          :default-col-def="detailDefaultColDef"
          :row-selection="detailRowSelection"
          height="100%"
        />
      </div>
    </div>
  </div>
</template>
